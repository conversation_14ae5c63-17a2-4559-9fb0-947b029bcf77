<?xml version="1.0" encoding="UTF-8"?>
<!--
SPDX-FileCopyrightText: 2023 UnionTech Software Technology Co., Ltd.
SPDX-License-Identifier: LGPL-3.0-or-later
-->
<!-- -*- XML -*- -->
<!DOCTYPE busconfig PUBLIC "-//freedesktop//DTD D-BUS Bus Configuration 1.0//EN" "http://www.freedesktop.org/standards/dbus/1.0/busconfig.dtd">
<busconfig>
  <!-- Allow any user to own the service -->
  <policy context="default">
    <allow own="org.desktopspec.ApplicationManager1"/>
    <allow send_destination="org.desktopspec.ApplicationManager1"/>
    <allow receive_sender="org.desktopspec.ApplicationManager1"/>
  </policy>
  
  <!-- Allow systemd to manage the service -->
  <policy user="root">
    <allow send_destination="org.desktopspec.ApplicationManager1"/>
    <allow receive_sender="org.desktopspec.ApplicationManager1"/>
  </policy>
  
  <!-- Allow session users to interact with the service -->
  <policy at_console="true">
    <allow send_destination="org.desktopspec.ApplicationManager1"/>
    <allow receive_sender="org.desktopspec.ApplicationManager1"/>
  </policy>
</busconfig>
